<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Hierarchical Account Structure</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="html/resourceaccount/resourceaccount_report.css">
</head>
<body class="bg-orange-50 text-gray-900 font-sans">
    <div class="min-h-screen">
        <header class="bg-orange-600 shadow-lg border-b border-orange-700">
            <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div class="flex justify-between items-center py-6">
                    <h1 class="text-3xl font-bold text-white">👥 Test Hierarchical Account Structure</h1>
                    <div class="text-sm text-orange-100">Generated: 2025-07-16 12:40:00</div>
                </div>
            </div>
        </header>
        <main class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-4">📊 Summary Statistics</h2>
                <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6" id="summaryStats"></div>
            </div>
            <div class="mb-8">
                <h2 class="text-2xl font-semibold text-gray-900 mb-4">🌳 Hierarchical Account Structure</h2>
                <div id="accountTree"></div>
            </div>
        </main>
    </div>
    <script>
        // Mock hierarchical data for testing
        window.resourceAccountTree = [
            {
                "folder_id": "root",
                "folder_name": "Root (No Folder)",
                "create_time": null,
                "type": "folder",
                "accounts": [
                    {
                        "name": "Root Account 1",
                        "id": "*********",
                        "status": "CreateSuccess",
                        "type": "ResourceAccount",
                        "join_method": "created",
                        "join_time": "2024-01-01T00:00:00Z",
                        "modify_time": "2024-01-01T00:00:00Z",
                        "display_name": "Root Account 1",
                        "account_id": "*********",
                        "account_name": "root-account-1"
                    }
                ],
                "children": []
            },
            {
                "folder_id": "fd-BWUR3Yo26Y",
                "folder_name": "ali.blizzard.com",
                "create_time": "2024-02-06T03:21:22.340Z",
                "type": "folder",
                "accounts": [
                    {
                        "name": "Production Account",
                        "id": "*********",
                        "status": "CreateSuccess",
                        "type": "ResourceAccount",
                        "join_method": "created",
                        "join_time": "2024-02-06T04:00:00Z",
                        "modify_time": "2024-02-06T04:00:00Z",
                        "display_name": "Production Account",
                        "account_id": "*********",
                        "account_name": "prod-account"
                    }
                ],
                "children": [
                    {
                        "folder_id": "fd-subfolder1",
                        "folder_name": "Development",
                        "create_time": "2024-02-07T03:21:22.340Z",
                        "type": "folder",
                        "accounts": [
                            {
                                "name": "Dev Account 1",
                                "id": "*********",
                                "status": "CreateSuccess",
                                "type": "ResourceAccount",
                                "join_method": "invited",
                                "join_time": "2024-02-07T05:00:00Z",
                                "modify_time": "2024-02-07T05:00:00Z",
                                "display_name": "Dev Account 1",
                                "account_id": "*********",
                                "account_name": "dev-account-1"
                            },
                            {
                                "name": "Dev Account 2",
                                "id": "*********",
                                "status": "Suspended",
                                "type": "ResourceAccount",
                                "join_method": "created",
                                "join_time": "2024-02-08T05:00:00Z",
                                "modify_time": "2024-02-08T05:00:00Z",
                                "display_name": "Dev Account 2",
                                "account_id": "*********",
                                "account_name": "dev-account-2"
                            }
                        ],
                        "children": []
                    },
                    {
                        "folder_id": "fd-subfolder2",
                        "folder_name": "Testing",
                        "create_time": "2024-02-08T03:21:22.340Z",
                        "type": "folder",
                        "accounts": [
                            {
                                "name": "Test Account",
                                "id": "*********",
                                "status": "CreateSuccess",
                                "type": "ResourceAccount",
                                "join_method": "created",
                                "join_time": "2024-02-08T06:00:00Z",
                                "modify_time": "2024-02-08T06:00:00Z",
                                "display_name": "Test Account",
                                "account_id": "*********",
                                "account_name": "test-account"
                            }
                        ],
                        "children": []
                    }
                ]
            }
        ];
        
        window.resourceAccountStats = {
            "total_accounts": 5,
            "active_accounts": 4,
            "suspended_accounts": 1,
            "invited_accounts": 1,
            "created_accounts": 4,
            "total_folders": 4,
            "root_folders": 2,
            "TotalCount": 5,
            "RequestId": "test-request",
            "PageSize": 5,
            "PageNumber": 1
        };
    </script>
    <script src="html/resourceaccount/resourceaccount_report.js"></script>
</body>
</html>
