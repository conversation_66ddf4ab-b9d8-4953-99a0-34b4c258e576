def group_resources_by_account_and_type(resources):
    """
    Group resources by account name and resource type.
    Returns a dict: {account_name: {resource_type: [resources]}}
    """
    grouped = {}
    for res in resources:
        account_name = (
            res.get('account_name') or
            res.get('AccountName') or
            res.get('display_name') or
            res.get('DisplayName')
        )
        resource_type = res.get('resource_type') or res.get('ResourceType')
        if not account_name or not resource_type:
            continue
        if account_name not in grouped:
            grouped[account_name] = {}
        if resource_type not in grouped[account_name]:
            grouped[account_name][resource_type] = []
        grouped[account_name][resource_type].append(res)
    return grouped

# -*- coding: utf-8 -*-
"""
Alibaba Multi-Account Resource Center data module.
Handles resource data pulling and transformation for frontend consumption.
Contains AI-generated edits.
"""
import logging
from alibabacloud_resourcecenter20221201.client import Client as ResourceCenter20221201Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_resourcecenter20221201 import models as resource_center_20221201_models
from alibabacloud_tea_util import models as util_models
import json
from concurrent.futures import ThreadPoolExecutor
import time
import random
import re

def is_rate_limit_error(error):
    """
    Check if the error is a rate limit error based on error message.
    """
    error_msg = str(error).lower()
    rate_limit_indicators = [
        'user flow control',
        'rate limit',
        'throttling',
        'too many requests',
        'request was denied due to user flow control'
    ]
    return any(indicator in error_msg for indicator in rate_limit_indicators)

def retry_with_backoff(func, max_retries=3, base_delay=1, max_delay=300, rate_limit_delay=5400, logger=None):
    """
    Retry function with exponential backoff and special handling for rate limits.

    Args:
        func: Function to retry
        max_retries: Maximum number of retries for non-rate-limit errors
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds for exponential backoff
        rate_limit_delay: Delay in seconds for rate limit errors (90 minutes = 5400 seconds)
        logger: Logger instance
    """
    for attempt in range(max_retries + 1):
        try:
            return func()
        except Exception as error:
            if is_rate_limit_error(error):
                if logger:
                    logger.warning(f"🚫 Rate limit detected: {error}")
                    logger.warning(f"⏰ Waiting {rate_limit_delay} seconds (90 minutes) before retrying...")
                    logger.info("💡 This is normal behavior when API rate limits are reached. Please be patient.")
                time.sleep(rate_limit_delay)
                # After rate limit wait, try once more
                try:
                    if logger:
                        logger.info("🔄 Retrying after rate limit wait...")
                    return func()
                except Exception as retry_error:
                    if logger:
                        logger.error(f"❌ Failed even after rate limit wait: {retry_error}")
                    raise retry_error
            elif attempt < max_retries:
                delay = min(base_delay * (2 ** attempt) + random.uniform(0, 1), max_delay)
                if logger:
                    logger.warning(f"⚠️ Attempt {attempt + 1}/{max_retries + 1} failed: {error}")
                    logger.info(f"🔄 Retrying in {delay:.2f} seconds...")
                time.sleep(delay)
            else:
                if logger:
                    logger.error(f"❌ All {max_retries + 1} attempts failed. Last error: {error}")
                raise error

def get_account_id_name_map(credential, runtime, logger=None):
    """
    Fetch all accounts and return a mapping of account_id to account_name.
    """
    from alibabacloud_resourcedirectorymaster20220419.client import Client as ResourceDirectoryMaster20220419Client
    from alibabacloud_resourcedirectorymaster20220419 import models as resource_directory_master_20220419_models
    rd_client = ResourceDirectoryMaster20220419Client(open_api_models.Config(credential=credential, endpoint='resourcedirectory.aliyuncs.com'))
    account_map = {}
    next_token_acc = None
    page_size = 100
    page_number = 1
    while True:
        if next_token_acc:
            page_number += 1
            acc_request = resource_directory_master_20220419_models.ListAccountsRequest(
                page_size=page_size,
                page_number=page_number,
                max_results=100,
                next_token=str(next_token_acc)
            )
        else:
            acc_request = resource_directory_master_20220419_models.ListAccountsRequest(
                page_size=page_size,
                max_results=100,
                page_number=page_number
            )
        # Wrap API call with retry logic
        def make_accounts_request():
            return rd_client.list_accounts_with_options(acc_request, runtime)

        acc_result = retry_with_backoff(make_accounts_request, logger=logger)
        body = acc_result.body
        accounts_obj = getattr(body, 'accounts', None)
        if accounts_obj is not None:
            acc_list = getattr(accounts_obj, 'account', None)
            if isinstance(acc_list, list):
                for acc in acc_list:
                    if not isinstance(acc, dict) and hasattr(acc, '__dict__'):
                        acc = acc.__dict__
                    account_id = acc.get('account_id') if isinstance(acc, dict) else getattr(acc, 'account_id', None)
                    display_name = acc.get('display_name') if isinstance(acc, dict) else getattr(acc, 'display_name', None)
                    if account_id and display_name:
                        account_map[str(account_id)] = display_name
            elif acc_list is not None:
                acc = acc_list
                if not isinstance(acc, dict) and hasattr(acc, '__dict__'):
                    acc = acc.__dict__
                account_id = acc.get('account_id') if isinstance(acc, dict) else getattr(acc, 'account_id', None)
                display_name = acc.get('display_name') if isinstance(acc, dict) else getattr(acc, 'display_name', None)
                if account_id and display_name:
                    account_map[str(account_id)] = display_name
        next_token_acc = getattr(body, 'next_token', None) or getattr(body, 'NextToken', None)
        if not next_token_acc:
            break
    if logger:
        logger.info(f"Account map: {account_map}")
        logger.info(f"Account map keys: {list(account_map.keys())}")
        logger.debug(f"Full account_map: {account_map}")
        logger.info(f"Total accounts found: {len(account_map)}")
    return account_map

def fetch_multi_account_resources(rd_scope, region=None, account_filter=None, resource_type_filter=None, logger=None):
    """
    Fetch all multi-account resources using Alibaba Resource Center API.
    Returns a list of resource dicts.

    Args:
        rd_scope: Resource Directory scope
        region: Region to filter resources (optional)
        account_filter: Account name/display name to filter resources (optional)
        resource_type_filter: Resource type to filter resources (optional)
        logger: Logger instance (optional)
    """
    # If no region specified, fetch data for default regions
    if region is None:
        combined_results = []
        for r in ['cn-beijing', 'cn-chengdu', 'cn-shenzhen', 'cn-hangzhou']:
            combined_results.extend(fetch_multi_account_resources(rd_scope, r, account_filter, resource_type_filter, logger))
        return combined_results

    credential = CredentialClient()
    config = open_api_models.Config(credential=credential)
    config.endpoint = 'resourcecenter.aliyuncs.com'
    client = ResourceCenter20221201Client(config)
    runtime = util_models.RuntimeOptions()
    # Build account_id -> account_name map
    account_map = get_account_id_name_map(credential, runtime, logger)
    all_results = []
    next_token = None
    while True:
        # Build filter list for region and resource type
        filters = []
        if region:
            filters.append(resource_center_20221201_models.SearchMultiAccountResourcesRequestFilter(
                key='RegionId',
                match_type='Equals',
                value=[region]
            ))
        if resource_type_filter:
            filters.append(resource_center_20221201_models.SearchMultiAccountResourcesRequestFilter(
                key='ResourceType',
                match_type='Equals',
                value=[resource_type_filter]
            ))
        request_kwargs = {
            'scope': rd_scope,
            'max_results': 100,
            'filter': filters
        }
        if next_token:
            request_kwargs['next_token'] = next_token
        request = resource_center_20221201_models.SearchMultiAccountResourcesRequest(**request_kwargs)

        # Wrap API call with retry logic
        def make_search_request():
            return client.search_multi_account_resources_with_options(request, runtime)

        result = retry_with_backoff(make_search_request, logger=logger)
        body = result.body
        # Normalize body to dict for safe access
        if hasattr(body, '__dict__'):
            body_dict = body.__dict__
        elif isinstance(body, dict):
            body_dict = body
        else:
            body_dict = {}
        raw_resources = body_dict.get('Resources', []) or body_dict.get('resources', [])
        next_token = body_dict.get('NextToken') or body_dict.get('next_token')
        # Flatten resources and map account_name
        if isinstance(raw_resources, list):
            for res in raw_resources:
                res_dict = res.__dict__ if hasattr(res, '__dict__') else res
                account_id = res_dict.get('account_id')
                if logger:
                    logger.info(f"Resource account_id: {account_id}")
                    logger.debug(f"Trying to map account_id {account_id} to name: {account_map.get(account_id)}")
                if account_id and account_id in account_map:
                    res_dict['account_name'] = account_map[account_id]
                all_results.append(res_dict)
        elif raw_resources:
            res_dict = raw_resources.__dict__ if hasattr(raw_resources, '__dict__') else raw_resources
            account_id = res_dict.get('account_id')
            if logger:
                logger.info(f"Resource account_id: {account_id}")
                logger.debug(f"Trying to map account_id {account_id} to name: {account_map.get(account_id)}")
            if account_id and account_id in account_map:
                res_dict['account_name'] = account_map[account_id]
            all_results.append(res_dict)
        if not next_token:
            break

    # Apply account filter if specified
    if account_filter:
        if logger:
            logger.info(f"Applying account filter: {account_filter}")
        filtered_results = []
        for res in all_results:
            account_name = res.get('account_name', '')
            # Check if the account name contains the filter string (case-insensitive)
            if account_filter.lower() in account_name.lower():
                filtered_results.append(res)
        all_results = filtered_results
        if logger:
            logger.info(f"After account filtering, resource count: {len(all_results)}")

    if logger:
        logger.info(f"Fetched all multi-account resources, total count: {len(all_results)}")
        logger.info("Starting concurrent fetching of resource configurations")
    # Fetch configurations concurrently
    def enrich_resource(res_dict):
        def fetch_config():
            config_request = resource_center_20221201_models.GetMultiAccountResourceConfigurationRequest()
            if 'resource_id' in res_dict:
                config_request.resource_id = res_dict['resource_id']
            if 'account_id' in res_dict:
                config_request.account_id = res_dict['account_id']
            if 'region_id' in res_dict:
                config_request.resource_region_id = res_dict['region_id']
            elif 'RegionId' in res_dict:
                config_request.resource_region_id = res_dict['RegionId']
            elif region:
                config_request.resource_region_id = region
            if 'resource_type' in res_dict:
                config_request.resource_type = res_dict['resource_type']
            return client.get_multi_account_resource_configuration_with_options(config_request, runtime)

        try:
            config_result = retry_with_backoff(fetch_config, logger=logger)
            if hasattr(config_result, 'body'):
                cfg_body = config_result.body if isinstance(config_result.body, dict) else getattr(config_result.body, '__dict__', {})
                res_dict['configuration'] = json.dumps(cfg_body, default=str)
        except Exception as error:
            if logger:
                logger.warning(f"Failed to fetch configuration for resource {res_dict.get('resource_id')}: {getattr(error, 'message', str(error))}")
        return res_dict

    # Reduce max_workers to avoid hitting rate limits too frequently
    with ThreadPoolExecutor(max_workers=3) as executor:
        all_results = list(executor.map(enrich_resource, all_results))

    # Adjust result: account name as first column
    for res in all_results:
        if 'account_name' in res:
            res['AccountName'] = res.pop('account_name')
        elif 'display_name' in res:
            res['AccountName'] = res.pop('display_name')
        if 'ResourceType' in res:
            res.pop('ResourceType')
    return all_results

def calculate_multiaccount_stats(resources):
    """
    Calculate summary statistics for multi-account resources.
    Returns a dict of stats.
    """
    stats = {
        'TotalResources': len(resources)
    }
    # Optionally, count unique accounts if AccountId is present
    account_ids = set()
    for res in resources:
        if 'AccountId' in res:
            account_ids.add(res['AccountId'])
    stats['TotalAccounts'] = len(account_ids)
    return stats

# Contains AI-generated edits.
