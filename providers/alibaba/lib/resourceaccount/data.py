# -*- coding: utf-8 -*-
"""
Alibaba Resource Directory Account data module.
Handles account data pulling and transformation for frontend consumption.
"""
import logging
from alibabacloud_resourcedirectorymaster20220419.client import Client as ResourceDirectoryMaster20220419Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_resourcedirectorymaster20220419 import models as resource_directory_master_20220419_models
from alibabacloud_tea_util import models as util_models
import json
import time
import random


def is_rate_limit_error(error):
    """
    Check if the error is a rate limit error based on error message.
    """
    error_msg = str(error).lower()
    rate_limit_indicators = [
        'user flow control',
        'rate limit',
        'throttling',
        'too many requests',
        'request was denied due to user flow control'
    ]
    return any(indicator in error_msg for indicator in rate_limit_indicators)


def retry_with_backoff(func, max_retries=3, base_delay=1, max_delay=300, rate_limit_delay=5400, logger=None):
    """
    Retry function with exponential backoff and special handling for rate limits.

    Args:
        func: Function to retry
        max_retries: Maximum number of retries for non-rate-limit errors
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds for exponential backoff
        rate_limit_delay: Delay in seconds for rate limit errors (90 minutes = 5400 seconds)
        logger: Logger instance
    """
    for attempt in range(max_retries + 1):
        try:
            return func()
        except Exception as error:
            if is_rate_limit_error(error):
                if logger:
                    logger.warning(f"🚫 Rate limit detected: {error}")
                    logger.warning(f"⏰ Waiting {rate_limit_delay} seconds (90 minutes) before retrying...")
                    logger.info("💡 This is normal behavior when API rate limits are reached. Please be patient.")
                time.sleep(rate_limit_delay)
                # After rate limit wait, try once more
                try:
                    if logger:
                        logger.info("🔄 Retrying after rate limit wait...")
                    return func()
                except Exception as retry_error:
                    if logger:
                        logger.error(f"❌ Failed even after rate limit wait: {retry_error}")
                    raise retry_error
            elif attempt < max_retries:
                delay = min(base_delay * (2 ** attempt) + random.uniform(0, 1), max_delay)
                if logger:
                    logger.warning(f"⚠️ Attempt {attempt + 1}/{max_retries + 1} failed: {error}")
                    logger.info(f"🔄 Retrying in {delay:.2f} seconds...")
                time.sleep(delay)
            else:
                if logger:
                    logger.error(f"❌ All {max_retries + 1} attempts failed. Last error: {error}")
                raise error


def fetch_resource_directory_accounts(account_filter=None, logger=None):
    """
    Fetch all accounts from Alibaba Resource Directory.
    Returns a list of account dicts.

    Args:
        account_filter: Account name/display name to filter accounts (optional)
        logger: Logger instance (optional)
    """
    credential = CredentialClient()
    config = open_api_models.Config(credential=credential)
    config.endpoint = 'resourcedirectory.aliyuncs.com'
    
    rd_client = ResourceDirectoryMaster20220419Client(config)
    runtime = util_models.RuntimeOptions()
    
    all_accounts = []
    metadata = {}
    next_token = None
    page_size = 100
    page_number = 1
    
    while True:
        if next_token:
            page_number += 1
            acc_request = resource_directory_master_20220419_models.ListAccountsRequest(
                page_size=page_size,
                page_number=page_number,
                max_results=100,
                next_token=str(next_token)
            )
        else:
            acc_request = resource_directory_master_20220419_models.ListAccountsRequest(
                page_size=page_size,
                max_results=100,
                page_number=page_number
            )
        
        # Wrap API call with retry logic
        def make_accounts_request():
            return rd_client.list_accounts_with_options(acc_request, runtime)

        acc_result = retry_with_backoff(make_accounts_request, logger=logger)
        body = acc_result.body
        # Capture metadata from the first API response
        if not metadata:
            metadata = {
                'TotalCount': getattr(body, 'TotalCount', None) or getattr(body, 'total_count', None),
                'RequestId': getattr(body, 'RequestId', None) or getattr(body, 'requestId', None),
                'PageSize': getattr(body, 'PageSize', None) or getattr(body, 'pageSize', None),
                'PageNumber': getattr(body, 'PageNumber', None) or getattr(body, 'pageNumber', None)
            }
        accounts_obj = getattr(body, 'accounts', None)
        
        if accounts_obj is not None:
            acc_list = getattr(accounts_obj, 'account', None)
            if isinstance(acc_list, list):
                for acc in acc_list:
                    account_dict = _normalize_account_object(acc)
                    if account_dict:
                        all_accounts.append(account_dict)
            elif acc_list is not None:
                account_dict = _normalize_account_object(acc_list)
                if account_dict:
                    all_accounts.append(account_dict)
        
        next_token = getattr(body, 'next_token', None) or getattr(body, 'NextToken', None)
        if not next_token:
            break
    
    # Apply account filtering if specified
    if account_filter:
        if logger:
            logger.info(f"Applying account filter: {account_filter}")
        filtered_accounts = []
        for acc in all_accounts:
            account_name = acc.get('display_name', '')
            # Check if the account name contains the filter string (case-insensitive)
            if account_filter.lower() in account_name.lower():
                filtered_accounts.append(acc)
        all_accounts = filtered_accounts
        if logger:
            logger.info(f"After account filtering, account count: {len(all_accounts)}")
    
    if logger:
        logger.info(f"Fetched all accounts, total count: {len(all_accounts)}")
    
    return all_accounts, metadata


def _normalize_account_object(acc):
    """
    Normalize account object to a consistent dict format.
    """
    if not isinstance(acc, dict) and hasattr(acc, '__dict__'):
        acc = acc.__dict__
    
    if not isinstance(acc, dict):
        return None
    
    account_id = acc.get('account_id') or acc.get('AccountId')
    display_name = acc.get('display_name') or acc.get('DisplayName')
    account_name = acc.get('account_name') or acc.get('AccountName')
    status = acc.get('status') or acc.get('Status')
    type_field = acc.get('type') or acc.get('Type')
    join_method = acc.get('join_method') or acc.get('JoinMethod')
    join_time = acc.get('join_time') or acc.get('JoinTime')
    modify_time = acc.get('modify_time') or acc.get('ModifyTime')
    
    if not account_id or not display_name:
        return None
    
    return {
        'account_id': str(account_id),
        'display_name': display_name,
        'account_name': account_name or display_name,
        'status': status,
        'type': type_field,
        'join_method': join_method,
        'join_time': str(join_time) if join_time else None,
        'modify_time': str(modify_time) if modify_time else None
    }


def calculate_account_stats(accounts):
    """
    Calculate summary statistics for accounts.
    Returns a dict of stats.
    """
    stats = {
        'total_accounts': len(accounts),
        'active_accounts': 0,
        'suspended_accounts': 0,
        'invited_accounts': 0,
        'created_accounts': 0
    }
    
    for acc in accounts:
        status = acc.get('status', '').lower()
        join_method = acc.get('join_method', '').lower()
        
        if status == 'createdsuccess':
            stats['active_accounts'] += 1
        elif status == 'suspended':
            stats['suspended_accounts'] += 1
        
        if join_method == 'invited':
            stats['invited_accounts'] += 1
        elif join_method == 'created':
            stats['created_accounts'] += 1
    
    return stats


def group_accounts_by_status(accounts):
    """
    Group accounts by status.
    Returns a dict: {status: [accounts]}
    """
    grouped = {}
    for acc in accounts:
        status = acc.get('status', 'Unknown')
        if status not in grouped:
            grouped[status] = []
        grouped[status].append(acc)
    return grouped
