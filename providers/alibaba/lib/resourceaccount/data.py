# -*- coding: utf-8 -*-
"""
Alibaba Resource Directory Account data module.
Handles account data pulling and transformation for frontend consumption.
Now supports hierarchical folder structure.
"""
import logging
from alibabacloud_resourcedirectorymaster20220419.client import Client as ResourceDirectoryMaster20220419Client
from alibabacloud_credentials.client import Client as CredentialClient
from alibabacloud_tea_openapi import models as open_api_models
from alibabacloud_resourcedirectorymaster20220419 import models as resource_directory_master_20220419_models
from alibabacloud_tea_util import models as util_models
import json
import time
import random


def is_rate_limit_error(error):
    """
    Check if the error is a rate limit error based on error message.
    """
    error_msg = str(error).lower()
    rate_limit_indicators = [
        'user flow control',
        'rate limit',
        'throttling',
        'too many requests',
        'request was denied due to user flow control'
    ]
    return any(indicator in error_msg for indicator in rate_limit_indicators)


def retry_with_backoff(func, max_retries=3, base_delay=1, max_delay=300, rate_limit_delay=5400, logger=None):
    """
    Retry function with exponential backoff and special handling for rate limits.

    Args:
        func: Function to retry
        max_retries: Maximum number of retries for non-rate-limit errors
        base_delay: Base delay in seconds
        max_delay: Maximum delay in seconds for exponential backoff
        rate_limit_delay: Delay in seconds for rate limit errors (90 minutes = 5400 seconds)
        logger: Logger instance
    """
    for attempt in range(max_retries + 1):
        try:
            return func()
        except Exception as error:
            if is_rate_limit_error(error):
                if logger:
                    logger.warning(f"🚫 Rate limit detected: {error}")
                    logger.warning(f"⏰ Waiting {rate_limit_delay} seconds (90 minutes) before retrying...")
                    logger.info("💡 This is normal behavior when API rate limits are reached. Please be patient.")
                time.sleep(rate_limit_delay)
                # After rate limit wait, try once more
                try:
                    if logger:
                        logger.info("🔄 Retrying after rate limit wait...")
                    return func()
                except Exception as retry_error:
                    if logger:
                        logger.error(f"❌ Failed even after rate limit wait: {retry_error}")
                    raise retry_error
            elif attempt < max_retries:
                delay = min(base_delay * (2 ** attempt) + random.uniform(0, 1), max_delay)
                if logger:
                    logger.warning(f"⚠️ Attempt {attempt + 1}/{max_retries + 1} failed: {error}")
                    logger.info(f"🔄 Retrying in {delay:.2f} seconds...")
                time.sleep(delay)
            else:
                if logger:
                    logger.error(f"❌ All {max_retries + 1} attempts failed. Last error: {error}")
                raise error


def fetch_resource_directory_accounts(account_filter=None, logger=None):
    """
    Fetch all accounts from Alibaba Resource Directory.
    Returns a list of account dicts.

    Args:
        account_filter: Account name/display name to filter accounts (optional)
        logger: Logger instance (optional)
    """
    credential = CredentialClient()
    config = open_api_models.Config(credential=credential)
    config.endpoint = 'resourcedirectory.aliyuncs.com'
    
    rd_client = ResourceDirectoryMaster20220419Client(config)
    runtime = util_models.RuntimeOptions()
    
    all_accounts = []
    metadata = {}
    next_token = None
    page_size = 100
    page_number = 1
    
    while True:
        if next_token:
            page_number += 1
            acc_request = resource_directory_master_20220419_models.ListAccountsRequest(
                page_size=page_size,
                page_number=page_number,
                max_results=100,
                next_token=str(next_token)
            )
        else:
            acc_request = resource_directory_master_20220419_models.ListAccountsRequest(
                page_size=page_size,
                max_results=100,
                page_number=page_number
            )
        
        # Wrap API call with retry logic
        def make_accounts_request():
            return rd_client.list_accounts_with_options(acc_request, runtime)

        acc_result = retry_with_backoff(make_accounts_request, logger=logger)
        body = acc_result.body
        # Capture metadata from the first API response
        if not metadata:
            metadata = {
                'TotalCount': getattr(body, 'TotalCount', None) or getattr(body, 'total_count', None),
                'RequestId': getattr(body, 'RequestId', None) or getattr(body, 'requestId', None),
                'PageSize': getattr(body, 'PageSize', None) or getattr(body, 'pageSize', None),
                'PageNumber': getattr(body, 'PageNumber', None) or getattr(body, 'pageNumber', None)
            }
        accounts_obj = getattr(body, 'accounts', None)
        
        if accounts_obj is not None:
            acc_list = getattr(accounts_obj, 'account', None)
            if isinstance(acc_list, list):
                for acc in acc_list:
                    account_dict = _normalize_account_object(acc)
                    if account_dict:
                        all_accounts.append(account_dict)
            elif acc_list is not None:
                account_dict = _normalize_account_object(acc_list)
                if account_dict:
                    all_accounts.append(account_dict)
        
        next_token = getattr(body, 'next_token', None) or getattr(body, 'NextToken', None)
        if not next_token:
            break
    
    # Apply account filtering if specified
    if account_filter:
        if logger:
            logger.info(f"Applying account filter: {account_filter}")
        filtered_accounts = []
        for acc in all_accounts:
            account_name = acc.get('display_name', '')
            # Check if the account name contains the filter string (case-insensitive)
            if account_filter.lower() in account_name.lower():
                filtered_accounts.append(acc)
        all_accounts = filtered_accounts
        if logger:
            logger.info(f"After account filtering, account count: {len(all_accounts)}")
    
    if logger:
        logger.info(f"Fetched all accounts, total count: {len(all_accounts)}")
    
    return all_accounts, metadata


def _normalize_account_object(acc):
    """
    Normalize account object to a consistent dict format.
    """
    if not isinstance(acc, dict) and hasattr(acc, '__dict__'):
        acc = acc.__dict__
    
    if not isinstance(acc, dict):
        return None
    
    account_id = acc.get('account_id') or acc.get('AccountId')
    display_name = acc.get('display_name') or acc.get('DisplayName')
    account_name = acc.get('account_name') or acc.get('AccountName')
    status = acc.get('status') or acc.get('Status')
    type_field = acc.get('type') or acc.get('Type')
    join_method = acc.get('join_method') or acc.get('JoinMethod')
    join_time = acc.get('join_time') or acc.get('JoinTime')
    modify_time = acc.get('modify_time') or acc.get('ModifyTime')
    
    if not account_id or not display_name:
        return None
    
    return {
        'account_id': str(account_id),
        'display_name': display_name,
        'account_name': account_name or display_name,
        'status': status,
        'type': type_field,
        'join_method': join_method,
        'join_time': str(join_time) if join_time else None,
        'modify_time': str(modify_time) if modify_time else None
    }


def calculate_account_stats(accounts):
    """
    Calculate summary statistics for accounts.
    Returns a dict of stats.
    """
    stats = {
        'total_accounts': len(accounts),
        'active_accounts': 0,
        'suspended_accounts': 0,
        'invited_accounts': 0,
        'created_accounts': 0
    }

    for acc in accounts:
        status = acc.get('status', '').lower()
        join_method = acc.get('join_method', '').lower()

        if status == 'createdsuccess':
            stats['active_accounts'] += 1
        elif status == 'suspended':
            stats['suspended_accounts'] += 1

        if join_method == 'invited':
            stats['invited_accounts'] += 1
        elif join_method == 'created':
            stats['created_accounts'] += 1

    return stats


def create_resource_directory_client():
    """
    Create and return a Resource Directory client.
    """
    credential = CredentialClient()
    config = open_api_models.Config(credential=credential)
    config.endpoint = 'resourcedirectory.aliyuncs.com'
    return ResourceDirectoryMaster20220419Client(config)


def fetch_root_folders(client, runtime, logger=None):
    """
    Fetch root folders (folders without parent).
    Returns a list of folder dicts.
    """
    if logger:
        logger.info("Fetching root folders...")

    request = resource_directory_master_20220419_models.ListFoldersForParentRequest()

    def make_request():
        return client.list_folders_for_parent_with_options(request, runtime)

    try:
        result = retry_with_backoff(make_request, logger=logger)
        body = result.body

        folders = []
        folders_obj = getattr(body, 'folders', None)
        if folders_obj:
            folder_list = getattr(folders_obj, 'folder', None)
            if isinstance(folder_list, list):
                for folder in folder_list:
                    folder_dict = _normalize_folder_object(folder)
                    if folder_dict:
                        folders.append(folder_dict)
            elif folder_list:
                folder_dict = _normalize_folder_object(folder_list)
                if folder_dict:
                    folders.append(folder_dict)

        if logger:
            logger.info(f"Found {len(folders)} root folders")
        return folders

    except Exception as e:
        if logger:
            logger.error(f"Error fetching root folders: {e}")
        return []


def fetch_folders_for_parent(client, runtime, parent_folder_id, logger=None):
    """
    Fetch child folders for a given parent folder.
    Returns a list of folder dicts.
    """
    if logger:
        logger.debug(f"Fetching folders for parent: {parent_folder_id}")

    request = resource_directory_master_20220419_models.ListFoldersForParentRequest(
        parent_folder_id=parent_folder_id
    )

    def make_request():
        return client.list_folders_for_parent_with_options(request, runtime)

    try:
        result = retry_with_backoff(make_request, logger=logger)
        body = result.body

        folders = []
        folders_obj = getattr(body, 'folders', None)
        if folders_obj:
            folder_list = getattr(folders_obj, 'folder', None)
            if isinstance(folder_list, list):
                for folder in folder_list:
                    folder_dict = _normalize_folder_object(folder)
                    if folder_dict:
                        folders.append(folder_dict)
            elif folder_list:
                folder_dict = _normalize_folder_object(folder_list)
                if folder_dict:
                    folders.append(folder_dict)

        return folders

    except Exception as e:
        if logger:
            logger.debug(f"Error fetching folders for parent {parent_folder_id}: {e}")
        return []


def fetch_accounts_for_parent(client, runtime, parent_folder_id, logger=None):
    """
    Fetch accounts directly under a given parent folder.
    Returns a list of account dicts.
    """
    if logger:
        logger.debug(f"Fetching accounts for parent: {parent_folder_id}")

    request = resource_directory_master_20220419_models.ListAccountsForParentRequest(
        parent_folder_id=parent_folder_id
    )

    def make_request():
        return client.list_accounts_for_parent_with_options(request, runtime)

    try:
        result = retry_with_backoff(make_request, logger=logger)
        body = result.body

        accounts = []
        accounts_obj = getattr(body, 'accounts', None)
        if accounts_obj:
            account_list = getattr(accounts_obj, 'account', None)
            if isinstance(account_list, list):
                for account in account_list:
                    account_dict = _normalize_account_object(account)
                    if account_dict:
                        accounts.append(account_dict)
            elif account_list:
                account_dict = _normalize_account_object(account_list)
                if account_dict:
                    accounts.append(account_dict)

        return accounts

    except Exception as e:
        if logger:
            logger.debug(f"Error fetching accounts for parent {parent_folder_id}: {e}")
        return []


def _normalize_folder_object(folder):
    """
    Normalize folder object to a consistent dict format.
    """
    if not isinstance(folder, dict) and hasattr(folder, '__dict__'):
        folder = folder.__dict__

    if not isinstance(folder, dict):
        return None

    folder_id = folder.get('folder_id') or folder.get('FolderId')
    folder_name = folder.get('folder_name') or folder.get('FolderName')
    create_time = folder.get('create_time') or folder.get('CreateTime')

    if not folder_id or not folder_name:
        return None

    return {
        'folder_id': str(folder_id),
        'folder_name': folder_name,
        'create_time': str(create_time) if create_time else None,
        'type': 'folder'
    }


def build_hierarchical_tree(client, runtime, account_filter=None, logger=None):
    """
    Build a hierarchical tree structure of folders and accounts.
    Returns a tree structure starting from root folders.
    """
    if logger:
        logger.info("Building hierarchical folder and account tree...")

    # Start with root folders
    root_folders = fetch_root_folders(client, runtime, logger)
    tree = []
    all_accounts = []

    def build_folder_tree(folder, depth=0):
        """Recursively build tree for a folder and its children."""
        if logger and depth < 3:  # Avoid too much debug output for deep trees
            logger.debug(f"{'  ' * depth}Processing folder: {folder['folder_name']}")

        # Get accounts directly under this folder
        accounts = fetch_accounts_for_parent(client, runtime, folder['folder_id'], logger)

        # Filter accounts if account_filter is provided
        if account_filter:
            filtered_accounts = []
            for acc in accounts:
                display_name = acc.get('display_name', '').lower()
                account_name = acc.get('account_name', '').lower()
                if (account_filter.lower() in display_name or
                    account_filter.lower() in account_name):
                    filtered_accounts.append(acc)
            accounts = filtered_accounts

        # Add accounts to global list for stats calculation
        all_accounts.extend(accounts)

        # Get child folders
        child_folders = fetch_folders_for_parent(client, runtime, folder['folder_id'], logger)

        # Build tree structure for this folder
        folder_node = {
            **folder,
            'accounts': accounts,
            'children': []
        }

        # Recursively process child folders
        for child_folder in child_folders:
            child_node = build_folder_tree(child_folder, depth + 1)
            folder_node['children'].append(child_node)

        return folder_node

    # Build tree for each root folder
    for root_folder in root_folders:
        tree_node = build_folder_tree(root_folder)
        tree.append(tree_node)

    # Also get accounts at the root level (not in any folder)
    try:
        root_accounts = fetch_accounts_for_parent(client, runtime, None, logger)
        if account_filter:
            filtered_root_accounts = []
            for acc in root_accounts:
                display_name = acc.get('display_name', '').lower()
                account_name = acc.get('account_name', '').lower()
                if (account_filter.lower() in display_name or
                    account_filter.lower() in account_name):
                    filtered_root_accounts.append(acc)
            root_accounts = filtered_root_accounts

        all_accounts.extend(root_accounts)

        if root_accounts:
            # Add a virtual root node for accounts not in folders
            root_node = {
                'folder_id': 'root',
                'folder_name': 'Root (No Folder)',
                'create_time': None,
                'type': 'folder',
                'accounts': root_accounts,
                'children': []
            }
            tree.insert(0, root_node)  # Insert at beginning

    except Exception as e:
        if logger:
            logger.debug(f"Could not fetch root-level accounts: {e}")

    if logger:
        logger.info(f"Built hierarchical tree with {len(tree)} root nodes and {len(all_accounts)} total accounts")

    return tree, all_accounts


def fetch_resource_directory_accounts_hierarchical(account_filter=None, logger=None):
    """
    Fetch all accounts from Alibaba Resource Directory in hierarchical structure.
    Returns a tuple of (hierarchical_tree, flat_account_list, metadata).

    Args:
        account_filter: Account name/display name to filter accounts (optional)
        logger: Logger instance (optional)
    """
    client = create_resource_directory_client()
    runtime = util_models.RuntimeOptions()

    try:
        tree, all_accounts = build_hierarchical_tree(client, runtime, account_filter, logger)

        # Create metadata (simplified since we're not using pagination for the tree)
        metadata = {
            'TotalCount': len(all_accounts),
            'RequestId': 'hierarchical-request',
            'PageSize': len(all_accounts),
            'PageNumber': 1
        }

        return tree, all_accounts, metadata

    except Exception as e:
        if logger:
            logger.error(f"Error fetching hierarchical accounts: {e}")
        raise


def calculate_hierarchical_stats(tree, all_accounts):
    """
    Calculate summary statistics for hierarchical account data.
    Returns a dict of stats including folder counts.
    """
    def count_folders(node):
        """Recursively count folders in tree."""
        count = 1  # Count this folder
        for child in node.get('children', []):
            count += count_folders(child)
        return count

    total_folders = sum(count_folders(node) for node in tree)

    stats = calculate_account_stats(all_accounts)
    stats.update({
        'total_folders': total_folders,
        'root_folders': len(tree)
    })

    return stats


def group_accounts_by_status(accounts):
    """
    Group accounts by status.
    Returns a dict: {status: [accounts]}
    """
    grouped = {}
    for acc in accounts:
        status = acc.get('status', 'Unknown')
        if status not in grouped:
            grouped[status] = []
        grouped[status].append(acc)
    return grouped
