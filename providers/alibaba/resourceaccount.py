# -*- coding: utf-8 -*-
# Alibaba resourceaccount subcommand
# This file is auto-generated, don't edit it. Thanks.

import sys
import argparse
import logging
import traceback
import os

from providers.alibaba.lib.resourceaccount import data as account_data
from providers.alibaba.lib.resourceaccount import html_report as account_html

def setup_logging(debug=False):
    level = logging.DEBUG if debug else logging.INFO
    logging.basicConfig(
        level=level,
        format='%(asctime)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s'
    )
    return logging.getLogger(__name__)

def parse_args():
    parser = argparse.ArgumentParser(
        description="Alibaba Resource Directory account lister",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python -m providers.alibaba.resourceaccount                    # List all accounts
  python -m providers.alibaba.resourceaccount --debug           # Enable debug output
  python -m providers.alibaba.resourceaccount -a blz-p-ow-game  # Filter by account name
"""
    )
    parser.add_argument(
        "--debug", "-d",
        action="store_true",
        help="Enable debug logging to show detailed debugging information"
    )
    parser.add_argument(
        "--account", "-a",
        type=str,
        default=None,
        help="Filter accounts by account name/display name (e.g. blz-p-ow-game). If not set, fetch data for all accounts."
    )
    return parser.parse_args()

def main():
    """Main entry point for Alibaba Resource Directory account lister."""
    args = None
    logger = None
    try:
        args = parse_args()
        logger = setup_logging(args.debug)
        # Attach account filter to logger for downstream usage
        setattr(logger, 'account', args.account)
        logger.info("Starting Alibaba Resource Directory account listing...")

        # Fetch accounts using the new hierarchical structure
        account_filter = getattr(logger, 'account', None)
        tree, accounts, metadata = account_data.fetch_resource_directory_accounts_hierarchical(account_filter=account_filter, logger=logger)

        # Calculate statistics
        stats = account_data.calculate_hierarchical_stats(tree, accounts)
        # Merge metadata into stats for reporting
        stats.update(metadata)

        # Generate HTML report with hierarchical data
        output_file = account_html.generate_resourceaccount_html_report(tree, accounts, stats)
        abs_path = os.path.abspath(output_file)

        logger.info(f"📄 Report generated successfully: {abs_path}")
        print(f"📄 Report generated: {abs_path}")

        # Also print summary to console
        print(f"✅ Found {stats['total_accounts']} accounts in {stats['total_folders']} folders")
        print(f"   - Active: {stats['active_accounts']}")
        print(f"   - Suspended: {stats['suspended_accounts']}")
        print(f"   - Invited: {stats['invited_accounts']}")
        print(f"   - Created: {stats['created_accounts']}")
        print(f"   - Root folders: {stats['root_folders']}")

    except KeyboardInterrupt:
        if logger is None:
            logger = logging.getLogger(__name__)
        logger.info("🛑 Operation cancelled by user")
        print("🛑 Operation cancelled by user")
        sys.exit(0)
    except Exception as e:
        if logger is None:
            logger = logging.getLogger(__name__)
        logger.error(f"❌ Unexpected error: {e}")
        if args is not None and getattr(args, "debug", False):
            logger.debug(f"Traceback: {traceback.format_exc()}")
            print(f"Traceback: {traceback.format_exc()}")
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()