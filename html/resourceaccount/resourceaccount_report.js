/**
 * Resource Directory Account Report - Table Renderer
 */
class ResourceAccountReport {
    constructor() {
        this.data = window.resourceAccountData;
        this.stats = window.resourceAccountStats;
        this.init();
    }

    init() {
        this.renderSummaryStats();
        this.renderTable();
    }

    renderSummaryStats() {
        const container = document.getElementById('summaryStats');
        const statCards = [
            { icon: '👥', number: this.stats.total_accounts.toLocaleString(), label: 'Total Accounts', color: 'orange' },
            { icon: '✅', number: this.stats.active_accounts.toLocaleString(), label: 'Active Accounts', color: 'green' },
            { icon: '⏸️', number: this.stats.suspended_accounts.toLocaleString(), label: 'Suspended Accounts', color: 'red' },
            { icon: '📊', number: this.stats.total_statuses.toLocaleString(), label: 'Account Statuses', color: 'orange' }
        ];
        container.innerHTML = statCards.map(card => `
            <div class="stat-card">
                <div class="flex items-center">
                    <div class="text-2xl mr-3">${card.icon}</div>
                    <div>
                        <div class="stat-number text-${card.color}-400">${card.number}</div>
                        <div class="stat-label">${card.label}</div>
                    </div>
                </div>
            </div>
        `).join('');
    }

    renderTable() {
        const container = document.getElementById('accountTable');
        let html = `
            <div class="mb-4">
                <input id="accountSearch" type="text" placeholder="Search..." class="border py-2 px-4 rounded w-full" />
            </div>
            <table class="w-full text-sm border-collapse">
                <thead>
                    <tr class="bg-orange-100">
                        <th class="py-2 px-4 text-left">Display Name</th>
                        <th class="py-2 px-4 text-left">Account ID</th>
                        <th class="py-2 px-4 text-left">Status</th>
                        <th class="py-2 px-4 text-left">Type</th>
                        <th class="py-2 px-4 text-left">Join Method</th>
                        <th class="py-2 px-4 text-left">Join Time</th>
                        <th class="py-2 px-4 text-left">Modify Time</th>
                    </tr>
                </thead>
                <tbody>`;
        this.data.forEach(statusGroup => {
            statusGroup.accounts.forEach(acc => {
                html += `
                    <tr class="border-b border-gray-200 hover:bg-gray-100">
                        <td class="py-2 px-4">${acc.name || 'Unknown'}</td>
                        <td class="py-2 px-4">${acc.id || 'Unknown'}</td>
                        <td class="py-2 px-4">${statusGroup.status_name}</td>
                        <td class="py-2 px-4">${acc.type || 'Unknown'}</td>
                        <td class="py-2 px-4">${acc.join_method || 'Unknown'}</td>
                        <td class="py-2 px-4">${acc.join_time || 'Unknown'}</td>
                        <td class="py-2 px-4">${acc.modify_time || 'Unknown'}</td>
                    </tr>`;
            });
        });
        html += `
                </tbody>
            </table>`;
        container.innerHTML = html;
        const searchInput = document.getElementById('accountSearch');
        searchInput.addEventListener('input', () => {
            const term = searchInput.value.toLowerCase();
            const rows = container.querySelectorAll('tbody tr');
            rows.forEach(row => {
                const text = row.textContent.toLowerCase();
                row.style.display = text.includes(term) ? '' : 'none';
            });
        });
    }
}

let resourceAccountReport;
document.addEventListener('DOMContentLoaded', () => {
    resourceAccountReport = new ResourceAccountReport();
});
