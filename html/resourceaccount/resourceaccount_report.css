/* Import Azure Resource Overview CSS for identical layout */
@import url("../resource_overview/resource_overview_report.css");

/* Tree View Specific Styles */
.tree-container {
    max-height: 80vh;
    overflow-y: auto;
    border: 1px solid #e5e7eb;
}

.tree-node {
    border-left: 2px solid transparent;
}

.tree-node:hover {
    border-left-color: #f97316;
}

.folder-header {
    transition: all 0.2s ease;
    user-select: none;
}

.folder-header:hover {
    background-color: #fef3e2 !important;
}

.folder-icon {
    font-size: 1.1em;
}

.account-icon {
    font-size: 0.9em;
    opacity: 0.8;
}

.expand-icon {
    font-size: 0.8em;
    transition: transform 0.2s ease;
    display: inline-block;
    width: 12px;
    text-align: center;
}

.account-row {
    transition: all 0.2s ease;
    border-left: 3px solid transparent;
}

.account-row:hover {
    border-left-color: #3b82f6;
    background-color: #eff6ff !important;
}

.status-badge {
    font-size: 0.75rem;
    font-weight: 500;
    display: inline-block;
    border-radius: 0.375rem;
}

/* Indentation classes for tree levels */
.ml-0 { margin-left: 0; }
.ml-4 { margin-left: 1rem; }
.ml-8 { margin-left: 2rem; }
.ml-12 { margin-left: 3rem; }
.ml-16 { margin-left: 4rem; }
.ml-20 { margin-left: 5rem; }
.ml-24 { margin-left: 6rem; }
.ml-28 { margin-left: 7rem; }
.ml-32 { margin-left: 8rem; }

/* Responsive adjustments */
@media (max-width: 768px) {
    .tree-container {
        max-height: 60vh;
    }

    .account-row .grid {
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
    }

    .folder-header {
        font-size: 0.9rem;
    }

    .account-row {
        font-size: 0.8rem;
    }
}

/* Search highlighting */
.search-highlight {
    background-color: #fef08a;
    padding: 0.1rem 0.2rem;
    border-radius: 0.25rem;
}

/* Loading states */
.tree-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;
    color: #6b7280;
}

.tree-loading::before {
    content: "🔄";
    animation: spin 1s linear infinite;
    margin-right: 0.5rem;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

/* Empty state */
.tree-empty {
    text-align: center;
    padding: 3rem;
    color: #6b7280;
}

.tree-empty::before {
    content: "📁";
    display: block;
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}
